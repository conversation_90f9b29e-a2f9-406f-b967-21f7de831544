pluginManagement {
    def flutterSdkPath = {
        def properties = new Properties()
        file("local.properties").withInputStream { properties.load(it) }
        def flutterSdkPath = properties.getProperty("flutter.sdk")
        assert flutterSdkPath != null, "flutter.sdk not set in local.properties"
        return flutterSdkPath
    }()

    includeBuild("$flutterSdkPath/packages/flutter_tools/gradle")

    repositories {
        google()
        mavenCentral()
        gradlePluginPortal()
    }

    plugins {
        id 'com.android.application' version '8.3.0'
        id 'com.android.library' version '8.3.0'
        id 'org.jetbrains.kotlin.android' version '1.9.22'
    }
}

plugins {
    id "dev.flutter.flutter-plugin-loader" version "1.0.0"
}

include ":app"
