// Mocks generated by <PERSON><PERSON>to 5.4.5 from annotations
// in trustchain_auth/test/features/banking/presentation/bloc/security_score_cubit_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i3;

import 'package:mockito/mockito.dart' as _i1;
import 'package:trustchain_auth/core/enums.dart' as _i6;
import 'package:trustchain_auth/features/auth/domain/models/auth_models.dart'
    as _i4;
import 'package:trustchain_auth/features/auth/domain/models/security_score.dart'
    as _i5;
import 'package:trustchain_auth/features/auth/domain/services/continuous_auth_service.dart'
    as _i2;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

/// A class which mocks [ContinuousAuthService].
///
/// See the documentation for Mockito's code generation for more information.
class MockContinuousAuthService extends _i1.Mock
    implements _i2.ContinuousAuthService {
  MockContinuousAuthService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i3.Stream<_i4.DomainAuthEvent> get authEvents => (super.noSuchMethod(
        Invocation.getter(#authEvents),
        returnValue: _i3.Stream<_i4.DomainAuthEvent>.empty(),
      ) as _i3.Stream<_i4.DomainAuthEvent>);

  @override
  _i3.Stream<_i5.SecurityScore> get securityScoreStream => (super.noSuchMethod(
        Invocation.getter(#securityScoreStream),
        returnValue: _i3.Stream<_i5.SecurityScore>.empty(),
      ) as _i3.Stream<_i5.SecurityScore>);

  @override
  _i3.Future<double> getCurrentConfidenceLevel() => (super.noSuchMethod(
        Invocation.method(
          #getCurrentConfidenceLevel,
          [],
        ),
        returnValue: _i3.Future<double>.value(0.0),
      ) as _i3.Future<double>);

  @override
  _i3.Future<void> startMonitoring() => (super.noSuchMethod(
        Invocation.method(
          #startMonitoring,
          [],
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  void stopMonitoring() => super.noSuchMethod(
        Invocation.method(
          #stopMonitoring,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void recordActivity() => super.noSuchMethod(
        Invocation.method(
          #recordActivity,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  double calculateRiskScore({
    required double? trustScore,
    required List<_i4.DomainAuthEvent>? recentEvents,
    required String? currentLocation,
    required bool? isKnownDevice,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #calculateRiskScore,
          [],
          {
            #trustScore: trustScore,
            #recentEvents: recentEvents,
            #currentLocation: currentLocation,
            #isKnownDevice: isKnownDevice,
          },
        ),
        returnValue: 0.0,
      ) as double);

  @override
  _i6.RiskLevel getRiskLevel(double? riskScore) => (super.noSuchMethod(
        Invocation.method(
          #getRiskLevel,
          [riskScore],
        ),
        returnValue: _i6.RiskLevel.low,
      ) as _i6.RiskLevel);

  @override
  void dispose() => super.noSuchMethod(
        Invocation.method(
          #dispose,
          [],
        ),
        returnValueForMissingStub: null,
      );
}
