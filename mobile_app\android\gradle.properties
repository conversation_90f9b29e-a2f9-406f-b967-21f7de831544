org.gradle.jvmargs=-Xmx4G -XX:MaxMetaspaceSize=2G -XX:ReservedCodeCacheSize=512m -XX:+HeapDumpOnOutOfMemoryError
android.useAndroidX=true
android.enableJetifier=true

# Java toolchain configuration - Use Java 17 for compatibility
org.gradle.java.home=C:\\Program Files\\Android\\Android Studio\\jbr
# Use Java 17 for Android builds (most stable and widely supported)
org.gradle.java.installations.auto-detect=false
# Force Java 17 compatibility
org.gradle.java.toolchain.version=17
