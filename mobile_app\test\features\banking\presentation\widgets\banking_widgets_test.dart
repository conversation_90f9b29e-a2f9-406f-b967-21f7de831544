import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/material.dart';
import 'package:mockito/mockito.dart';
import 'package:trustchain_auth/features/banking/presentation/widgets/account_summary_card.dart';
import 'package:trustchain_auth/features/banking/presentation/widgets/transactions_list.dart';
import 'package:trustchain_auth/features/banking/domain/models/models.dart';

class MockBankingBloc extends Mock implements BankingBloc {}

void main() {
  late MockBankingBloc mockBloc;

  setUp(() {
    mockBloc = MockBankingBloc();
  });

  group('AccountSummaryCard Widget Tests', () {
    testWidgets('displays account balance correctly', (WidgetTester tester) async {
      final account = Account(
        id: '1',
        userId: 'user1',
        balance: 1000.0,
        currency: 'USD',
        type: AccountType.checking,
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: AccountSummaryCard(account: account),
          ),
        ),
      );

      expect(find.text('\$1,000.00'), findsOneWidget);
      expect(find.text('Checking Account'), findsOneWidget);
    });

    testWidgets('handles null balance gracefully', (WidgetTester tester) async {
      final account = Account(
        id: '1',
        userId: 'user1',
        balance: null,
        currency: 'USD',
        type: AccountType.checking,
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: AccountSummaryCard(account: account),
          ),
        ),
      );

      expect(find.text('\$0.00'), findsOneWidget);
    });
  });

  group('TransactionsList Widget Tests', () {
    final transactions = [
      Transaction(
        id: '1',
        userId: 'user1',
        amount: 100.0,
        type: TransactionType.credit,
        description: 'Deposit',
        timestamp: DateTime.now(),
      ),
      Transaction(
        id: '2',
        userId: 'user1',
        amount: -50.0,
        type: TransactionType.debit,
        description: 'Withdrawal',
        timestamp: DateTime.now(),
      ),
    ];

    testWidgets('displays transactions correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: TransactionsList(transactions: transactions),
          ),
        ),
      );

      expect(find.text('Deposit'), findsOneWidget);
      expect(find.text('Withdrawal'), findsOneWidget);
      expect(find.text('\$100.00'), findsOneWidget);
      expect(find.text('-\$50.00'), findsOneWidget);
    });

    testWidgets('shows empty state when no transactions',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: TransactionsList(transactions: []),
          ),
        ),
      );

      expect(find.text('No transactions yet'), findsOneWidget);
    });
  });

  group('TransferDialog Widget Tests', () {
    testWidgets('validates input fields', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) => ElevatedButton(
              onPressed: () => showDialog(
                context: context,
                builder: (context) => TransferDialog(),
              ),
              child: Text('Show Dialog'),
            ),
          ),
        ),
      );

      await tester.tap(find.text('Show Dialog'));
      await tester.pumpAndSettle();

      // Try to submit without input
      await tester.tap(find.text('Transfer'));
      await tester.pump();

      expect(find.text('Amount is required'), findsOneWidget);
      expect(find.text('Recipient is required'), findsOneWidget);

      // Fill invalid amount
      await tester.enterText(find.byType(TextField).first, '-100');
      await tester.pump();

      expect(find.text('Amount must be positive'), findsOneWidget);
    });
  });
}
